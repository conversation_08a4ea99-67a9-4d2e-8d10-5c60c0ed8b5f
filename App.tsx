import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  Platform,
} from 'react-native';
import RNFS from 'react-native-fs';
import {request, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {Audio} from '@react-native-community/audio-toolkit';
import * as ort from 'onnxruntime-react-native';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Ready');
  const [isSpeaking, setIsSpeaking] = useState(false);
  let session = null;
  let tokenMap = {};

  useEffect(() => {
    // Request audio permissions
    const requestAudioPermission = async () => {
      const permission =
        Platform.OS === 'ios'
          ? PERMISSIONS.IOS.MICROPHONE
          : PERMISSIONS.ANDROID.RECORD_AUDIO;
      const result = await request(permission);
      console.log('Audio permission:', result);
      if (result !== RESULTS.GRANTED) {
        setStatus('Audio permission denied. TTS may not work.');
      }
    };
    requestAudioPermission();

    // Initialize ONNX Runtime session and load tokenizer
    async function initTTS() {
      setStatus('Loading model...');
      try {
        // Determine asset path based on platform
        const basePath =
          Platform.OS === 'ios' ? RNFS.MainBundlePath : 'file:///android_asset'; // Use asset path for Android
        const modelPath = `${basePath}/kokoro-model/kokoro_q8.onnx`;
        const tokensPath = `${basePath}/kokoro-model/tokens.txt`;
        const voicesPath = `${basePath}/kokoro-model/voices.bin`;

        // Verify files exist
        for (const path of [modelPath, tokensPath, voicesPath]) {
          const exists = await RNFS.exists(path);
          console.log(`Checking ${path}: ${exists}`);
          if (!exists) {
            throw new Error(`File not found: ${path}`);
          }
        }

        // Initialize ONNX Runtime session
        session = await ort.InferenceSession.create(modelPath, {
          executionProviders: ['nnapi', 'cpu'], // Use NNAPI for Android, fallback to CPU
          graphOptimizationLevel: 'all',
          enableMemPattern: true,
          intraOpNumThreads: 2,
        });

        // Load tokens.txt
        const tokens = await RNFS.readFile(tokensPath, 'utf8');
        tokenMap = tokens
          .split('\n')
          .reduce((acc, token, i) => ({...acc, [token]: i}), {});

        setStatus('Model loaded');
      } catch (error) {
        console.error('Error loading model:', error);
        setStatus('Failed to load model: ' + error.message);
      }
    }
    initTTS();

    return () => {
      if (session) session.release();
    };
  }, []);

  const chunkText = text => {
    // Split text into sentences for batch processing
    return text.match(/[^.!?]+[.!?]+/g) || [text];
  };

  const synthesizeSpeech = async () => {
    if (!text) {
      setStatus('Please enter text');
      return;
    }
    if (!session) {
      setStatus('Model not loaded');
      return;
    }
    if (isSpeaking) {
      setStatus('Already speaking');
      return;
    }

    setIsSpeaking(true);
    setStatus('Generating speech...');

    try {
      const chunks = chunkText(text); // Split into sentences
      let audioContext = null;
      let currentSource = null;

      for (const chunk of chunks) {
        if (!isSpeaking) break; // Allow stopping mid-process

        // Preprocess text to token IDs
        const textTokens = chunk.split('').map(char => tokenMap[char] || 0); // Adjust based on Kokoro's tokenizer
        const inputTensor = new ort.Tensor('int32', textTokens, [
          1,
          textTokens.length,
        ]);

        // Load voices.bin (assuming speaker embeddings)
        const voicesPath =
          Platform.OS === 'ios'
            ? `${RNFS.MainBundlePath}/kokoro-model/voices.bin`
            : `file:///android_asset/kokoro-model/voices.bin`;
        const voicesData = await RNFS.readFile(voicesPath, 'base64');
        const voicesTensor = new ort.Tensor(
          'float32',
          new Float32Array(Buffer.from(voicesData, 'base64')),
          [1 /* adjust dimensions */],
        );

        // Run inference
        const outputs = await session.run({
          input: inputTensor,
          speaker_embedding: voicesTensor, // Adjust key based on Kokoro's model
        });
        const audioData = outputs.output.data; // Adjust based on Kokoro's output format

        // Initialize audio context if not already done
        if (!audioContext) {
          audioContext = new AudioContext();
        }

        // Convert to audio buffer
        const audioBuffer = await audioContext.decodeAudioData(
          new Uint8Array(audioData).buffer,
        );
        currentSource = audioContext.createBufferSource();
        currentSource.buffer = audioBuffer;
        currentSource.connect(audioContext.destination);
        currentSource.start();

        // Wait for playback to complete before next chunk
        await new Promise(resolve => {
          currentSource.onended = resolve;
        });
      }

      setStatus('Ready');
      setIsSpeaking(false);
    } catch (error) {
      console.error('Error generating speech:', error);
      setStatus('Error generating speech: ' + error.message);
      setIsSpeaking(false);
    }
  };

  const stopSpeaking = () => {
    setIsSpeaking(false);
    setStatus('Stopped');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.status}>{status}</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter text to speak"
        onChangeText={setText}
        value={text}
        multiline
      />
      <View style={styles.buttonContainer}>
        <Button
          title="Speak"
          onPress={synthesizeSpeech}
          disabled={isSpeaking || !text}
        />
        <Button title="Stop" onPress={stopSpeaking} disabled={!isSpeaking} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, padding: 20, justifyContent: 'center'},
  input: {borderWidth: 1, padding: 10, marginBottom: 10, height: 100},
  status: {marginBottom: 10, textAlign: 'center'},
  buttonContainer: {flexDirection: 'row', justifyContent: 'space-around'},
});

export default App;
