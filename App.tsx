import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import RNFS from 'react-native-fs';
import * as ort from 'onnxruntime-react-native';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing...');
  const [filesChecked, setFilesChecked] = useState(false);
  const [onnxSession, setOnnxSession] = useState<ort.InferenceSession | null>(
    null,
  );
  const [tokenMap, setTokenMap] = useState<{[key: string]: number}>({});
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeTTS();
  }, []);

  const initializeTTS = async () => {
    try {
      // Step 1: Check asset files
      await checkAssetFiles();

      // Step 2: Initialize ONNX Runtime session
      await initializeONNX();

      // Step 3: Load tokenizer
      await loadTokenizer();

      setIsInitialized(true);
      setStatus('✅ TTS Ready - Enter text and press Speak!');
    } catch (error) {
      console.error('TTS initialization failed:', error);
      setStatus('❌ TTS initialization failed: ' + (error as Error).message);
    }
  };

  const checkAssetFiles = async () => {
    try {
      setStatus('Checking asset files...');

      if (Platform.OS === 'android') {
        // For Android, try to read the files directly from assets
        const assetFiles = [
          'custom/kokoro_q4.onnx',
          'custom/tokens.txt',
          'custom/voices.bin',
        ];

        console.log('Checking Android asset files...');

        for (const fileName of assetFiles) {
          try {
            if (fileName.endsWith('.onnx')) {
              // For ONNX files, skip reading due to large size - will verify when loading with ONNX Runtime
              console.log(
                `⏭️ Skipping large ONNX file read: ${fileName} (will verify during TTS initialization)`,
              );
              console.log(
                `✅ Android asset ${fileName}: ASSUMED TO EXIST (large file, will verify with ONNX Runtime)`,
              );
            } else {
              // For other files, read normally
              const encoding = fileName.endsWith('.txt') ? 'utf8' : 'base64';
              const content = await RNFS.readFileAssets(fileName, encoding);
              const size = content.length;
              console.log(
                `✅ Android asset ${fileName}: EXISTS (${size} ${
                  encoding === 'utf8' ? 'chars' : 'base64 chars'
                })`,
              );
            }
          } catch (error) {
            console.log(
              `❌ Android asset ${fileName}: ERROR -`,
              (error as Error).message,
            );
          }
        }
      } else {
        // iOS approach
        const basePath = RNFS.MainBundlePath;
        const files = [
          `${basePath}/custom/kokoro_q4.onnx`,
          `${basePath}/custom/tokens.txt`,
          `${basePath}/custom/voices.bin`,
        ];

        console.log('Checking iOS files in:', basePath);

        for (const filePath of files) {
          try {
            const exists = await RNFS.exists(filePath);
            console.log(`File ${filePath}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);

            if (exists) {
              const stats = await RNFS.stat(filePath);
              console.log(`File size: ${stats.size} bytes`);
            }
          } catch (error) {
            console.log(`Error checking ${filePath}:`, error);
          }
        }
      }

      setFilesChecked(true);
      setStatus('Asset files checked ✅');
    } catch (error) {
      console.error('Error checking asset files:', error);
      setStatus('Error checking files: ' + (error as Error).message);
      throw error;
    }
  };

  const initializeONNX = async () => {
    try {
      setStatus('Loading ONNX model...');
      console.log('🚀 Initializing ONNX Runtime session...');

      if (Platform.OS === 'android') {
        // For Android, copy the model to a writable location first
        setStatus('Copying ONNX model to writable location...');
        console.log('📋 Copying ONNX model from assets...');

        const modelFileName = 'kokoro_q4.onnx';
        const targetPath = `${RNFS.DocumentDirectoryPath}/${modelFileName}`;

        // Check if model already exists in documents
        const modelExists = await RNFS.exists(targetPath);

        if (!modelExists) {
          console.log('📥 Copying model to:', targetPath);

          try {
            // Try to read the model from assets
            console.log('📖 Reading ONNX model from assets...');
            const modelData = await RNFS.readFileAssets(
              'custom/kokoro_q4.onnx',
              'base64',
            );
            console.log(`📊 Model data size: ${modelData.length} base64 chars`);

            // Write to documents directory
            console.log('💾 Writing model to documents directory...');
            await RNFS.writeFile(targetPath, modelData, 'base64');
            console.log('✅ Model copied successfully');
          } catch (copyError) {
            console.error('❌ Failed to copy model:', copyError);

            // Try alternative file names
            console.log('🔍 Trying alternative ONNX file names...');
            const alternatives = [
              'custom/kokoro.onnx',
              'kokoro_q4.onnx',
              'kokoro.onnx',
            ];

            let copySuccess = false;
            for (const altFile of alternatives) {
              try {
                console.log(`📖 Trying to read: ${altFile}`);
                const altModelData = await RNFS.readFileAssets(
                  altFile,
                  'base64',
                );
                console.log(
                  `📊 Alternative model data size: ${altModelData.length} base64 chars`,
                );

                await RNFS.writeFile(targetPath, altModelData, 'base64');
                console.log(`✅ Model copied successfully from: ${altFile}`);
                copySuccess = true;
                break;
              } catch (altError) {
                console.log(
                  `❌ Failed to read ${altFile}:`,
                  (altError as Error).message,
                );
              }
            }

            if (!copySuccess) {
              throw new Error(
                'Could not find or copy ONNX model from any location',
              );
            }
          }
        } else {
          console.log('✅ Model already exists in documents directory');
        }

        // Now create ONNX session with the copied file
        console.log('🧠 Creating ONNX session with path:', targetPath);

        const session = await ort.InferenceSession.create(targetPath, {
          executionProviders: ['cpu'], // Start with CPU
          graphOptimizationLevel: 'all',
          enableMemPattern: false, // Disable to reduce memory usage
          intraOpNumThreads: 1, // Reduce threads to save memory
        });

        setOnnxSession(session);
        console.log('✅ ONNX session created successfully');
        setStatus('ONNX model loaded ✅');
      } else {
        // iOS approach
        const modelPath = `${RNFS.MainBundlePath}/custom/kokoro_q4.onnx`;
        console.log('📁 iOS Model path:', modelPath);

        const session = await ort.InferenceSession.create(modelPath, {
          executionProviders: ['cpu'],
          graphOptimizationLevel: 'all',
          enableMemPattern: false,
          intraOpNumThreads: 1,
        });

        setOnnxSession(session);
        console.log('✅ ONNX session created successfully');
        setStatus('ONNX model loaded ✅');
      }
    } catch (error) {
      console.error('❌ ONNX initialization failed:', error);
      setStatus('ONNX initialization failed: ' + (error as Error).message);
      throw error;
    }
  };

  const loadTokenizer = async () => {
    try {
      setStatus('Loading tokenizer...');
      console.log('📖 Loading tokenizer...');

      const tokensContent = await RNFS.readFileAssets(
        'custom/tokens.txt',
        'utf8',
      );
      const tokens = tokensContent.trim().split('\n');

      const tokenMapping: {[key: string]: number} = {};
      tokens.forEach((token, index) => {
        tokenMapping[token] = index;
      });

      setTokenMap(tokenMapping);
      console.log(`✅ Tokenizer loaded with ${tokens.length} tokens`);
      setStatus('Tokenizer loaded ✅');
    } catch (error) {
      console.error('❌ Tokenizer loading failed:', error);
      setStatus('Tokenizer loading failed: ' + (error as Error).message);
      throw error;
    }
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!isInitialized || !onnxSession) {
      Alert.alert('Error', 'TTS system not initialized yet');
      return;
    }

    try {
      setStatus('🎤 Generating speech...');
      console.log('🎤 Starting TTS for text:', text);

      // Simple tokenization - convert text to token IDs
      const textTokens = text.split('').map(char => tokenMap[char] || 0);
      console.log(`📝 Tokenized text: ${textTokens.length} tokens`);

      // Create input tensor
      const inputTensor = new ort.Tensor(
        'int64',
        new BigInt64Array(textTokens.map(t => BigInt(t))),
        [1, textTokens.length],
      );

      console.log('🧠 Running ONNX inference...');

      // Run inference
      const outputs = await onnxSession.run({
        input: inputTensor, // Adjust input name based on your model
      });

      console.log('✅ ONNX inference completed');
      console.log('📊 Output keys:', Object.keys(outputs));

      // For now, just log the output shape and indicate success
      const outputKey = Object.keys(outputs)[0];
      const outputTensor = outputs[outputKey];
      console.log(`📈 Output shape: [${outputTensor.dims.join(', ')}]`);

      setStatus('✅ Speech generated! (Audio playback not yet implemented)');

      // TODO: Convert output to audio and play
      Alert.alert(
        'Success!',
        'Speech generation completed! Audio playback will be implemented next.',
      );
    } catch (error) {
      console.error('❌ TTS generation failed:', error);
      setStatus('❌ TTS failed: ' + (error as Error).message);
      Alert.alert('TTS Error', (error as Error).message);
    }
  };

  const handleStop = () => {
    setStatus('Stopped');
    console.log('Stop button pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Kokoro TTS App</Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button
            title="Speak"
            onPress={handleSpeak}
            disabled={!text.trim() || !isInitialized}
          />
          <Button title="Stop" onPress={handleStop} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
  },
});

export default App;
