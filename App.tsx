import React from 'react';
import {View, Text, TextInput, Button, StyleSheet} from 'react-native';

const App = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.status}>Kokoro TTS App</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter text to speak"
        multiline
      />
      <View style={styles.buttonContainer}>
        <Button title="Speak" onPress={() => {}} />
        <Button title="Stop" onPress={() => {}} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, padding: 20, justifyContent: 'center'},
  input: {borderWidth: 1, padding: 10, marginBottom: 10, height: 100},
  status: {marginBottom: 10, textAlign: 'center'},
  buttonContainer: {flexDirection: 'row', justifyContent: 'space-around'},
});

export default App;
