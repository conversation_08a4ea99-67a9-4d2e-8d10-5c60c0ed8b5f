import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import RNFS from 'react-native-fs';
import * as ort from 'onnxruntime-react-native';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing...');
  const [filesChecked, setFilesChecked] = useState(false);
  // const [onnxSession, setOnnxSession] = useState<ort.InferenceSession | null>(null); // Disabled for mock TTS
  const [tokenMap, setTokenMap] = useState<{[key: string]: number}>({});
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeTTS();
  }, []);

  const initializeTTS = async () => {
    try {
      // Step 1: Check asset files
      await checkAssetFiles();

      // Step 2: Load tokenizer (skip ONNX for now)
      await loadTokenizer();

      // Step 3: Initialize mock TTS (skip ONNX model loading)
      await initializeMockTTS();

      setIsInitialized(true);
      setStatus('✅ TTS Ready - Enter text and press Speak!');
    } catch (error) {
      console.error('TTS initialization failed:', error);
      setStatus('❌ TTS initialization failed: ' + (error as Error).message);
    }
  };

  const checkAssetFiles = async () => {
    try {
      setStatus('Checking asset files...');

      if (Platform.OS === 'android') {
        // For Android, try to read the files directly from assets
        const assetFiles = [
          'custom/kokoro_q4.onnx',
          'custom/tokens.txt',
          'custom/voices.bin',
        ];

        console.log('Checking Android asset files...');

        for (const fileName of assetFiles) {
          try {
            if (fileName.endsWith('.onnx')) {
              // For ONNX files, skip reading due to large size - will verify when loading with ONNX Runtime
              console.log(
                `⏭️ Skipping large ONNX file read: ${fileName} (will verify during TTS initialization)`,
              );
              console.log(
                `✅ Android asset ${fileName}: ASSUMED TO EXIST (large file, will verify with ONNX Runtime)`,
              );
            } else {
              // For other files, read normally
              const encoding = fileName.endsWith('.txt') ? 'utf8' : 'base64';
              const content = await RNFS.readFileAssets(fileName, encoding);
              const size = content.length;
              console.log(
                `✅ Android asset ${fileName}: EXISTS (${size} ${
                  encoding === 'utf8' ? 'chars' : 'base64 chars'
                })`,
              );
            }
          } catch (error) {
            console.log(
              `❌ Android asset ${fileName}: ERROR -`,
              (error as Error).message,
            );
          }
        }
      } else {
        // iOS approach
        const basePath = RNFS.MainBundlePath;
        const files = [
          `${basePath}/custom/kokoro_q4.onnx`,
          `${basePath}/custom/tokens.txt`,
          `${basePath}/custom/voices.bin`,
        ];

        console.log('Checking iOS files in:', basePath);

        for (const filePath of files) {
          try {
            const exists = await RNFS.exists(filePath);
            console.log(`File ${filePath}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);

            if (exists) {
              const stats = await RNFS.stat(filePath);
              console.log(`File size: ${stats.size} bytes`);
            }
          } catch (error) {
            console.log(`Error checking ${filePath}:`, error);
          }
        }
      }

      setFilesChecked(true);
      setStatus('Asset files checked ✅');
    } catch (error) {
      console.error('Error checking asset files:', error);
      setStatus('Error checking files: ' + (error as Error).message);
      throw error;
    }
  };

  const initializeMockTTS = async () => {
    try {
      setStatus('Initializing TTS system...');
      console.log('🎭 Initializing Mock TTS (ONNX model loading skipped)');

      // Simulate some initialization time
      await new Promise(resolve => setTimeout(resolve, 1000));

      console.log('✅ Mock TTS initialized successfully');
      setStatus('Mock TTS initialized ✅');
    } catch (error) {
      console.error('❌ Mock TTS initialization failed:', error);
      setStatus('Mock TTS initialization failed: ' + (error as Error).message);
      throw error;
    }
  };

  // ONNX initialization disabled for mock TTS demo
  // const initializeONNX = async () => { ... };

  const loadTokenizer = async () => {
    try {
      setStatus('Loading tokenizer...');
      console.log('📖 Loading tokenizer...');

      const tokensContent = await RNFS.readFileAssets(
        'custom/tokens.txt',
        'utf8',
      );
      const tokens = tokensContent.trim().split('\n');

      const tokenMapping: {[key: string]: number} = {};
      tokens.forEach((token, index) => {
        tokenMapping[token] = index;
      });

      setTokenMap(tokenMapping);
      console.log(`✅ Tokenizer loaded with ${tokens.length} tokens`);
      setStatus('Tokenizer loaded ✅');
    } catch (error) {
      console.error('❌ Tokenizer loading failed:', error);
      setStatus('Tokenizer loading failed: ' + (error as Error).message);
      throw error;
    }
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!isInitialized) {
      Alert.alert('Error', 'TTS system not initialized yet');
      return;
    }

    try {
      setStatus('🎤 Generating speech...');
      console.log('🎤 Starting Mock TTS for text:', text);

      // Simulate tokenization
      const textTokens = text.split('').map(char => tokenMap[char] || 0);
      console.log(`📝 Tokenized text: ${textTokens.length} tokens`);
      console.log(
        `📝 Sample tokens: [${textTokens.slice(0, 10).join(', ')}...]`,
      );

      // Simulate processing time based on text length
      const processingTime = Math.min(text.length * 50, 3000); // 50ms per char, max 3s
      setStatus(`🧠 Processing ${text.length} characters...`);

      await new Promise(resolve => setTimeout(resolve, processingTime));

      // Simulate successful speech generation
      console.log('✅ Mock TTS generation completed');
      console.log(
        `📊 Generated audio for "${text.substring(0, 50)}${
          text.length > 50 ? '...' : ''
        }"`,
      );
      console.log(
        `📈 Simulated output: ${textTokens.length * 256} audio samples`,
      );

      setStatus('✅ Speech generated! (Mock TTS - Audio playback simulated)');

      // Show success with details
      Alert.alert(
        'TTS Success! 🎉',
        `Successfully processed "${text.substring(0, 100)}${
          text.length > 100 ? '...' : ''
        }"\n\n` +
          `• Text length: ${text.length} characters\n` +
          `• Tokens generated: ${textTokens.length}\n` +
          `• Processing time: ${processingTime}ms\n\n` +
          `Note: This is a mock TTS demo. Real audio generation will be implemented when the ONNX model loading issue is resolved.`,
        [
          {
            text: 'OK',
            onPress: () => setStatus('✅ Ready for next text'),
          },
        ],
      );
    } catch (error) {
      console.error('❌ Mock TTS generation failed:', error);
      setStatus('❌ TTS failed: ' + (error as Error).message);
      Alert.alert('TTS Error', (error as Error).message);
    }
  };

  const handleStop = () => {
    setStatus('Stopped');
    console.log('Stop button pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Kokoro TTS App</Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button
            title="Speak"
            onPress={handleSpeak}
            disabled={!text.trim() || !isInitialized}
          />
          <Button title="Stop" onPress={handleStop} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
  },
});

export default App;
