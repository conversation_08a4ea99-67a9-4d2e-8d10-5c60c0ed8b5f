import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState(
    'Ready - TTS functionality will be implemented',
  );

  const handleSpeak = () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    setStatus(`Would speak: "${text}"`);
    console.log('Speak button pressed with text:', text);

    // TODO: Implement TTS functionality
    Alert.alert('TTS', 'Text-to-Speech functionality will be implemented here');
  };

  const handleStop = () => {
    setStatus('Stopped');
    console.log('Stop button pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Kokoro TTS App</Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button title="Speak" onPress={handleSpeak} disabled={!text.trim()} />
          <Button title="Stop" onPress={handleStop} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1, padding: 20, justifyContent: 'center'},
  input: {borderWidth: 1, padding: 10, marginBottom: 10, height: 100},
  status: {marginBottom: 10, textAlign: 'center'},
  buttonContainer: {flexDirection: 'row', justifyContent: 'space-around'},
});

export default App;
