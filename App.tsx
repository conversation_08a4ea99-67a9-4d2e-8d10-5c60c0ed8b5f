import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import RNFS from 'react-native-fs';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing...');
  const [filesChecked, setFilesChecked] = useState(false);

  useEffect(() => {
    checkAssetFiles();
  }, []);

  const checkAssetFiles = async () => {
    try {
      setStatus('Checking asset files...');

      if (Platform.OS === 'android') {
        // For Android, try to read the files directly from assets
        const assetFiles = [
          'custom/kokoro_q4.onnx',
          'custom/tokens.txt',
          'custom/voices.bin',
        ];

        console.log('Checking Android asset files...');

        for (const fileName of assetFiles) {
          try {
            if (fileName.endsWith('.onnx')) {
              // For ONNX files, just try to read a small portion to verify existence
              console.log(`🔍 Checking large ONNX file: ${fileName}`);
              try {
                // Try to read just the first few bytes to verify file exists
                const header = await RNFS.readFileAssets(
                  fileName,
                  'base64',
                ).then(content => content.substring(0, 100));
                console.log(
                  `✅ Android asset ${fileName}: EXISTS (ONNX model file, header verified)`,
                );
              } catch (onnxError) {
                // If direct read fails, try alternatives
                console.log('🔍 Trying alternative ONNX file names...');
                const alternatives = [
                  'custom/kokoro.onnx',
                  'custom/kokoro_q4.onnx',
                ];
                let found = false;
                for (const alt of alternatives) {
                  try {
                    const header = await RNFS.readFileAssets(
                      alt,
                      'base64',
                    ).then(content => content.substring(0, 100));
                    console.log(
                      `✅ Found ONNX model: ${alt} (header verified)`,
                    );
                    found = true;
                    break;
                  } catch (altError) {
                    console.log(
                      `❌ Alternative ${alt}: ${(altError as Error).message}`,
                    );
                  }
                }
                if (!found) {
                  console.log(`❌ No ONNX model found`);
                }
              }
            } else {
              // For other files, read normally
              const encoding = fileName.endsWith('.txt') ? 'utf8' : 'base64';
              const content = await RNFS.readFileAssets(fileName, encoding);
              const size = content.length;
              console.log(
                `✅ Android asset ${fileName}: EXISTS (${size} ${
                  encoding === 'utf8' ? 'chars' : 'base64 chars'
                })`,
              );
            }
          } catch (error) {
            console.log(
              `❌ Android asset ${fileName}: ERROR -`,
              (error as Error).message,
            );
          }
        }
      } else {
        // iOS approach
        const basePath = RNFS.MainBundlePath;
        const files = [
          `${basePath}/custom/kokoro_q4.onnx`,
          `${basePath}/custom/tokens.txt`,
          `${basePath}/custom/voices.bin`,
        ];

        console.log('Checking iOS files in:', basePath);

        for (const filePath of files) {
          try {
            const exists = await RNFS.exists(filePath);
            console.log(`File ${filePath}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);

            if (exists) {
              const stats = await RNFS.stat(filePath);
              console.log(`File size: ${stats.size} bytes`);
            }
          } catch (error) {
            console.log(`Error checking ${filePath}:`, error);
          }
        }
      }

      setFilesChecked(true);
      setStatus('Ready - Asset files checked (see console)');
    } catch (error) {
      console.error('Error checking asset files:', error);
      setStatus('Error checking files: ' + (error as Error).message);
    }
  };

  const handleSpeak = () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!filesChecked) {
      Alert.alert('Error', 'Asset files not checked yet');
      return;
    }

    setStatus(
      `Preparing to speak: "${text.substring(0, 50)}${
        text.length > 50 ? '...' : ''
      }"`,
    );
    console.log('Speak button pressed with text:', text);

    // TODO: Implement actual TTS functionality
    setTimeout(() => {
      setStatus('TTS functionality will be implemented next');
    }, 1000);
  };

  const handleStop = () => {
    setStatus('Stopped');
    console.log('Stop button pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Kokoro TTS App</Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button title="Speak" onPress={handleSpeak} disabled={!text.trim()} />
          <Button title="Stop" onPress={handleStop} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
  },
});

export default App;
