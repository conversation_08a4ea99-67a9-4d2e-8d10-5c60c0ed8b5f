import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import Tts from 'react-native-tts';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing TTS...');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    initializeTTS();

    // Cleanup function
    return () => {
      Tts.removeAllListeners('tts-start');
      Tts.removeAllListeners('tts-finish');
      Tts.removeAllListeners('tts-cancel');
    };
  }, []);

  const initializeTTS = async () => {
    try {
      setStatus('🚀 Initializing Device TTS...');
      console.log('🚀 Initializing React Native TTS...');

      // Initialize TTS
      await Tts.getInitStatus();

      console.log('📦 TTS initialized successfully');
      setStatus('📦 Setting up TTS configuration...');

      // Set default TTS settings
      await Tts.setDefaultLanguage('en-US');
      await Tts.setDefaultRate(0.5);
      await Tts.setDefaultPitch(1.0);

      // Set up event listeners
      Tts.addEventListener('tts-start', () => {
        console.log('🎤 TTS started');
        setIsSpeaking(true);
        setStatus('🎤 Speaking...');
      });

      Tts.addEventListener('tts-finish', () => {
        console.log('✅ TTS finished');
        setIsSpeaking(false);
        setStatus('✅ Ready for next text');
      });

      Tts.addEventListener('tts-cancel', () => {
        console.log('⏹️ TTS cancelled');
        setIsSpeaking(false);
        setStatus('⏹️ Speech cancelled');
      });

      setIsInitialized(true);
      console.log('✅ TTS initialized successfully!');
      setStatus('✅ TTS Ready - Enter text and press Speak!');
    } catch (error) {
      console.error('❌ TTS initialization failed:', error);
      setStatus('❌ TTS initialization failed: ' + (error as Error).message);

      Alert.alert(
        'TTS Initialization Failed',
        `Error: ${
          (error as Error).message
        }\n\nThis might be due to:\n• TTS not available on device\n• Permissions issues\n• Native linking not configured`,
        [{text: 'OK'}],
      );
    }
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!isInitialized) {
      Alert.alert('Error', 'TTS not initialized yet');
      return;
    }

    if (isSpeaking) {
      Alert.alert(
        'Info',
        'Already speaking. Please wait or stop current speech.',
      );
      return;
    }

    try {
      setStatus('🎤 Starting speech...');
      console.log('🎤 Starting TTS for text:', text);

      // Speak the text using device TTS
      await Tts.speak(text);

      console.log('✅ TTS speak command sent');
    } catch (error) {
      console.error('❌ TTS speak failed:', error);
      setStatus('❌ TTS failed: ' + (error as Error).message);
      setIsSpeaking(false);
      Alert.alert('TTS Error', `Speech failed: ${(error as Error).message}`);
    }
  };

  const handleStop = () => {
    if (isSpeaking) {
      Tts.stop();
      console.log('⏹️ TTS stopped');
    } else {
      console.log('ℹ️ No speech to stop');
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>React Native TTS App</Text>
        <Text style={styles.subtitle}>
          Real Text-to-Speech using device's native TTS engine
        </Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button
            title={isSpeaking ? 'Speaking...' : 'Speak'}
            onPress={handleSpeak}
            disabled={!text.trim() || !isInitialized || isSpeaking}
          />
          <Button title="Stop" onPress={handleStop} disabled={!isSpeaking} />
        </View>

        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            🎤 This app uses react-native-tts to access your device's native TTS
            engine.
          </Text>
          <Text style={styles.infoText}>
            🔧 Supports multiple languages and voices based on your device
            settings.
          </Text>
          <Text style={styles.infoText}>
            ⚡ No internet required - uses offline TTS engine.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    fontStyle: 'italic',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
    marginBottom: 30,
  },
  infoContainer: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
    lineHeight: 16,
  },
});

export default App;
