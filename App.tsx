import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing TTS...');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    initializeTTS();
  }, []);

  const initializeTTS = async () => {
    try {
      setStatus('🚀 Initializing Mock TTS...');
      console.log('🚀 Initializing Mock TTS (no external dependencies)...');

      // Simulate initialization time
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('📦 Mock TTS initialized successfully');
      setStatus('📦 Mock TTS ready...');

      setIsInitialized(true);
      console.log('✅ Mock TTS initialized successfully!');
      setStatus('✅ Mock TTS Ready - Enter text and press Speak!');
      
    } catch (error) {
      console.error('❌ TTS initialization failed:', error);
      setStatus('❌ TTS initialization failed: ' + (error as Error).message);

      Alert.alert(
        'TTS Initialization Failed',
        `Error: ${(error as Error).message}`,
        [{text: 'OK'}],
      );
    }
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!isInitialized) {
      Alert.alert('Error', 'TTS not initialized yet');
      return;
    }

    if (isSpeaking) {
      Alert.alert('Info', 'Already speaking. Please wait or stop current speech.');
      return;
    }

    try {
      setStatus('🎤 Starting speech...');
      setIsSpeaking(true);
      console.log('🎤 Starting Mock TTS for text:', text);

      // Simulate speech duration based on text length
      const speechDuration = Math.max(text.length * 100, 2000); // 100ms per character, min 2s
      
      console.log(`📊 Simulating speech for ${speechDuration}ms`);
      
      // Simulate speaking
      await new Promise(resolve => setTimeout(resolve, speechDuration));
      
      setIsSpeaking(false);
      setStatus('✅ Speech completed!');
      console.log('✅ Mock TTS completed');

      // Show success message
      Alert.alert(
        'Mock TTS Success! 🎉',
        `Successfully "spoke" the text:\n"${text.substring(0, 100)}${
          text.length > 100 ? '...' : ''
        }"\n\n` +
          `• Text length: ${text.length} characters\n` +
          `• Simulated duration: ${speechDuration}ms\n\n` +
          `Note: This is a mock implementation. In a real app, this would use the device's native TTS engine.`,
        [
          {
            text: 'OK',
            onPress: () => setStatus('✅ Ready for next text'),
          },
        ],
      );
      
    } catch (error) {
      console.error('❌ Mock TTS failed:', error);
      setStatus('❌ TTS failed: ' + (error as Error).message);
      setIsSpeaking(false);
      Alert.alert('TTS Error', `Speech failed: ${(error as Error).message}`);
    }
  };

  const handleStop = () => {
    if (isSpeaking) {
      setIsSpeaking(false);
      setStatus('⏹️ Speech stopped');
      console.log('⏹️ Mock TTS stopped');
    } else {
      console.log('ℹ️ No speech to stop');
    }
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Mock TTS App</Text>
        <Text style={styles.subtitle}>
          Demonstrates TTS functionality without external dependencies
        </Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button
            title={isSpeaking ? "Speaking..." : "Speak"}
            onPress={handleSpeak}
            disabled={!text.trim() || !isInitialized || isSpeaking}
          />
          <Button 
            title="Stop" 
            onPress={handleStop} 
            disabled={!isSpeaking}
          />
        </View>
        
        <View style={styles.infoContainer}>
          <Text style={styles.infoText}>
            💡 This is a mock TTS implementation that simulates speech without requiring external libraries or native linking.
          </Text>
          <Text style={styles.infoText}>
            🔧 To implement real TTS, you would need to properly configure react-native-tts or use platform-specific solutions.
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    fontStyle: 'italic',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
    marginBottom: 30,
  },
  infoContainer: {
    backgroundColor: '#f0f0f0',
    padding: 15,
    borderRadius: 8,
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
    lineHeight: 16,
  },
});

export default App;
