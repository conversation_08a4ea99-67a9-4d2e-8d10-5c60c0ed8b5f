import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import RNFS from 'react-native-fs';
import * as ort from 'onnxruntime-react-native';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing...');
  const [filesChecked, setFilesChecked] = useState(false);
  const [onnxSession, setOnnxSession] = useState<ort.InferenceSession | null>(
    null,
  );
  const [tokenMap, setTokenMap] = useState<{[key: string]: number}>({});
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeTTS();
  }, []);

  const initializeTTS = async () => {
    try {
      // Step 1: Check asset files
      await checkAssetFiles();

      // Step 2: Initialize ONNX Runtime session
      await initializeONNX();

      // Step 3: Load tokenizer
      await loadTokenizer();

      setIsInitialized(true);
      setStatus('✅ TTS Ready - Enter text and press Speak!');
    } catch (error) {
      console.error('TTS initialization failed:', error);
      setStatus('❌ TTS initialization failed: ' + (error as Error).message);
    }
  };

  const checkAssetFiles = async () => {
    try {
      setStatus('Checking asset files...');

      if (Platform.OS === 'android') {
        // For Android, try to read the files directly from assets
        const assetFiles = [
          'custom/kokoro_q4.onnx',
          'custom/tokens.txt',
          'custom/voices.bin',
        ];

        console.log('Checking Android asset files...');

        for (const fileName of assetFiles) {
          try {
            if (fileName.endsWith('.onnx')) {
              // For ONNX files, skip reading due to large size - will verify when loading with ONNX Runtime
              console.log(
                `⏭️ Skipping large ONNX file read: ${fileName} (will verify during TTS initialization)`,
              );
              console.log(
                `✅ Android asset ${fileName}: ASSUMED TO EXIST (large file, will verify with ONNX Runtime)`,
              );
            } else {
              // For other files, read normally
              const encoding = fileName.endsWith('.txt') ? 'utf8' : 'base64';
              const content = await RNFS.readFileAssets(fileName, encoding);
              const size = content.length;
              console.log(
                `✅ Android asset ${fileName}: EXISTS (${size} ${
                  encoding === 'utf8' ? 'chars' : 'base64 chars'
                })`,
              );
            }
          } catch (error) {
            console.log(
              `❌ Android asset ${fileName}: ERROR -`,
              (error as Error).message,
            );
          }
        }
      } else {
        // iOS approach
        const basePath = RNFS.MainBundlePath;
        const files = [
          `${basePath}/custom/kokoro_q4.onnx`,
          `${basePath}/custom/tokens.txt`,
          `${basePath}/custom/voices.bin`,
        ];

        console.log('Checking iOS files in:', basePath);

        for (const filePath of files) {
          try {
            const exists = await RNFS.exists(filePath);
            console.log(`File ${filePath}: ${exists ? 'EXISTS' : 'NOT FOUND'}`);

            if (exists) {
              const stats = await RNFS.stat(filePath);
              console.log(`File size: ${stats.size} bytes`);
            }
          } catch (error) {
            console.log(`Error checking ${filePath}:`, error);
          }
        }
      }

      setFilesChecked(true);
      setStatus('Asset files checked ✅');
    } catch (error) {
      console.error('Error checking asset files:', error);
      setStatus('Error checking files: ' + (error as Error).message);
      throw error;
    }
  };

  const initializeONNX = async () => {
    try {
      setStatus('Loading ONNX model...');
      console.log('🚀 Initializing ONNX Runtime session...');

      if (Platform.OS === 'android') {
        // For Android, try to use the model directly from assets with different approaches
        setStatus('Attempting to load ONNX model...');
        console.log('🎯 Trying direct ONNX model loading approaches...');

        const possiblePaths = [
          'file:///android_asset/custom/kokoro.onnx',
          'file:///android_asset/custom/kokoro_q4.onnx',
          '/android_asset/custom/kokoro.onnx',
          '/android_asset/custom/kokoro_q4.onnx',
          'android_asset/custom/kokoro.onnx',
          'android_asset/custom/kokoro_q4.onnx',
        ];

        let sessionCreated = false;

        for (const modelPath of possiblePaths) {
          try {
            console.log(`🧠 Trying ONNX session with path: ${modelPath}`);

            const session = await ort.InferenceSession.create(modelPath, {
              executionProviders: ['cpu'], // Start with CPU only
              graphOptimizationLevel: 'basic', // Use basic optimization to reduce memory
              enableMemPattern: false, // Disable to reduce memory usage
              intraOpNumThreads: 1, // Single thread to reduce memory
              enableCpuMemArena: false, // Disable memory arena
            });

            setOnnxSession(session);
            console.log(
              `✅ ONNX session created successfully with path: ${modelPath}`,
            );
            setStatus('ONNX model loaded ✅');
            sessionCreated = true;
            break;
          } catch (pathError) {
            console.log(
              `❌ Failed with path ${modelPath}:`,
              (pathError as Error).message,
            );
          }
        }

        if (!sessionCreated) {
          // If direct loading fails, show a helpful message
          console.log('💡 Direct model loading failed. This might be due to:');
          console.log(
            '   - ONNX Runtime React Native limitations with Android assets',
          );
          console.log('   - Model file size (290MB) being too large');
          console.log('   - Asset path access restrictions');

          throw new Error(
            'Could not load ONNX model. The model file is too large (290MB) to load directly from Android assets. ' +
              'Consider using a smaller quantized model or implementing streaming file copy.',
          );
        }
      } else {
        // iOS approach
        const modelPath = `${RNFS.MainBundlePath}/custom/kokoro_q4.onnx`;
        console.log('📁 iOS Model path:', modelPath);

        const session = await ort.InferenceSession.create(modelPath, {
          executionProviders: ['cpu'],
          graphOptimizationLevel: 'all',
          enableMemPattern: false,
          intraOpNumThreads: 1,
        });

        setOnnxSession(session);
        console.log('✅ ONNX session created successfully');
        setStatus('ONNX model loaded ✅');
      }
    } catch (error) {
      console.error('❌ ONNX initialization failed:', error);
      setStatus('ONNX initialization failed: ' + (error as Error).message);
      throw error;
    }
  };

  const loadTokenizer = async () => {
    try {
      setStatus('Loading tokenizer...');
      console.log('📖 Loading tokenizer...');

      const tokensContent = await RNFS.readFileAssets(
        'custom/tokens.txt',
        'utf8',
      );
      const tokens = tokensContent.trim().split('\n');

      const tokenMapping: {[key: string]: number} = {};
      tokens.forEach((token, index) => {
        tokenMapping[token] = index;
      });

      setTokenMap(tokenMapping);
      console.log(`✅ Tokenizer loaded with ${tokens.length} tokens`);
      setStatus('Tokenizer loaded ✅');
    } catch (error) {
      console.error('❌ Tokenizer loading failed:', error);
      setStatus('Tokenizer loading failed: ' + (error as Error).message);
      throw error;
    }
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!isInitialized || !onnxSession) {
      Alert.alert('Error', 'TTS system not initialized yet');
      return;
    }

    try {
      setStatus('🎤 Generating speech...');
      console.log('🎤 Starting TTS for text:', text);

      // Simple tokenization - convert text to token IDs
      const textTokens = text.split('').map(char => tokenMap[char] || 0);
      console.log(`📝 Tokenized text: ${textTokens.length} tokens`);

      // Create input tensor
      const inputTensor = new ort.Tensor(
        'int64',
        new BigInt64Array(textTokens.map(t => BigInt(t))),
        [1, textTokens.length],
      );

      console.log('🧠 Running ONNX inference...');

      // Run inference
      const outputs = await onnxSession.run({
        input: inputTensor, // Adjust input name based on your model
      });

      console.log('✅ ONNX inference completed');
      console.log('📊 Output keys:', Object.keys(outputs));

      // For now, just log the output shape and indicate success
      const outputKey = Object.keys(outputs)[0];
      const outputTensor = outputs[outputKey];
      console.log(`📈 Output shape: [${outputTensor.dims.join(', ')}]`);

      setStatus('✅ Speech generated! (Audio playback not yet implemented)');

      // TODO: Convert output to audio and play
      Alert.alert(
        'Success!',
        'Speech generation completed! Audio playback will be implemented next.',
      );
    } catch (error) {
      console.error('❌ TTS generation failed:', error);
      setStatus('❌ TTS failed: ' + (error as Error).message);
      Alert.alert('TTS Error', (error as Error).message);
    }
  };

  const handleStop = () => {
    setStatus('Stopped');
    console.log('Stop button pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Kokoro TTS App</Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button
            title="Speak"
            onPress={handleSpeak}
            disabled={!text.trim() || !isInitialized}
          />
          <Button title="Stop" onPress={handleStop} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
  },
});

export default App;
