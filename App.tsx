import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  StyleSheet,
  SafeAreaView,
  Alert,
} from 'react-native';
import {pipeline} from '@fugood/transformers';

const App = () => {
  const [text, setText] = useState('');
  const [status, setStatus] = useState('Initializing Kokoro TTS...');
  const [isInitialized, setIsInitialized] = useState(false);
  const [kokoroTTS, setKokoroTTS] = useState<any>(null);

  useEffect(() => {
    initializeKokoroTTS();
  }, []);

  const initializeKokoroTTS = async () => {
    try {
      setStatus('🚀 Loading Kokoro TTS from Hugging Face...');
      console.log('🚀 Initializing Kokoro TTS with @fugood/transformers...');

      console.log('📦 Transformers imported successfully');
      setStatus('📦 Creating TTS pipeline...');

      // Create a text-to-speech pipeline
      const model_id = 'onnx-community/Kokoro-82M-v1.0-ONNX';
      console.log('🔧 Loading model from:', model_id);
      setStatus('⬇️ Downloading model from Hugging Face...');

      const tts = await pipeline('text-to-speech', model_id, {
        dtype: 'q8', // Use quantized model for better mobile performance
        device: 'wasm', // Use WASM for React Native compatibility
      });

      setKokoroTTS(tts);
      setIsInitialized(true);

      console.log('✅ Kokoro TTS initialized successfully!');
      setStatus('✅ Kokoro TTS Ready - Enter text and press Speak!');
    } catch (error) {
      console.error('❌ Kokoro TTS initialization failed:', error);
      setStatus('❌ TTS initialization failed: ' + (error as Error).message);

      // Show detailed error to user
      Alert.alert(
        'TTS Initialization Failed',
        `Error: ${
          (error as Error).message
        }\n\nThis might be due to:\n• Network connectivity issues\n• Model download problems\n• React Native compatibility issues`,
        [{text: 'OK'}],
      );
    }
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      Alert.alert('Error', 'Please enter some text to speak');
      return;
    }

    if (!isInitialized || !kokoroTTS) {
      Alert.alert('Error', 'Kokoro TTS not initialized yet');
      return;
    }

    try {
      setStatus('🎤 Generating speech with Kokoro TTS...');
      console.log('🎤 Starting Kokoro TTS for text:', text);

      // Generate speech using Kokoro TTS pipeline
      const audio = await kokoroTTS(text, {
        speaker_id: 0, // Use default speaker
      });

      console.log('✅ Kokoro TTS generation completed');
      console.log(`📊 Generated audio:`, audio);

      setStatus(
        '✅ Speech generated! (Audio playback will be implemented next)',
      );

      // Show success with details
      Alert.alert(
        'Kokoro TTS Success! 🎉',
        `Successfully generated speech for:\n"${text.substring(0, 100)}${
          text.length > 100 ? '...' : ''
        }"\n\n` +
          `• Text length: ${text.length} characters\n` +
          `• Speaker: Default (speaker_id: 0)\n` +
          `• Audio generated: ${audio ? 'Yes' : 'No'}\n\n` +
          `Note: Audio playback will be implemented next.`,
        [
          {
            text: 'OK',
            onPress: () => setStatus('✅ Ready for next text'),
          },
        ],
      );
    } catch (error) {
      console.error('❌ Kokoro TTS generation failed:', error);
      setStatus('❌ TTS failed: ' + (error as Error).message);
      Alert.alert(
        'TTS Error',
        `Kokoro TTS failed: ${(error as Error).message}`,
      );
    }
  };

  const handleStop = () => {
    setStatus('Stopped');
    console.log('Stop button pressed');
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <Text style={styles.title}>Kokoro TTS App</Text>
        <Text style={styles.status}>{status}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter text to speak..."
          value={text}
          onChangeText={setText}
          multiline
          numberOfLines={4}
        />
        <View style={styles.buttonContainer}>
          <Button
            title="Speak"
            onPress={handleSpeak}
            disabled={!text.trim() || !isInitialized}
          />
          <Button title="Stop" onPress={handleStop} />
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  status: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
    minHeight: 40,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    fontSize: 16,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    gap: 20,
  },
});

export default App;
